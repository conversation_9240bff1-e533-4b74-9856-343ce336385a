import { useChat } from 'ai/react'
import { useState, useEffect, useCallback, useRef } from 'react'
import type { Message } from 'ai'
import { FastAPIAdapter } from '@/lib/fastapi-adapter'
import type { TaskInfo, TaskError, StreamingState } from '@/types/task.types'
import { isStreamingSupported } from '@/utils/streaming'

interface UseVoiceCodeChatOptions {
  initialMessages?: Message[]
  onCommandExecute?: (command: string, sandboxId: string) => void
  onError?: (error: Error) => void
  // New streaming options
  enableStreaming?: boolean
  onTaskStart?: (task: TaskInfo) => void
  onTaskComplete?: (task: TaskInfo) => void
  onTaskError?: (error: TaskError) => void
}

interface UseVoiceCodeChatReturn {
  messages: Message[]
  input: string
  setInput: (value: string) => void
  handleSubmit: (e?: React.FormEvent) => void
  isLoading: boolean
  isLoadingHistory: boolean
  isSendingMessage: boolean
  error: string | null
  reload: () => void
  clearHistory: () => Promise<void>
  executeCommand: (command: string) => Promise<void>
  pagination: {
    page: number
    total: number
    hasNextPage: boolean
  }
  loadMoreHistory: () => Promise<void>
  // New streaming returns
  activeTask: TaskInfo | null
  cancelTask: () => void
  isStreaming: boolean
  streamingState: StreamingState
  // Message threading utilities
  getTaskMessages: (taskId: string) => Message[]
  getActiveThreads: () => Map<string, Message[]>
}

/**
 * Custom hook that bridges Vercel AI SDK with VoiceCode's FastAPI backend
 */
export function useVoiceCodeChat(
  sandboxId: string,
  options: UseVoiceCodeChatOptions = {}
): UseVoiceCodeChatReturn {
  const [adapter] = useState(() => new FastAPIAdapter())
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)
  const [isExecutingCommand, setIsExecutingCommand] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    total: 0,
    hasNextPage: false
  })
  const [historyLoaded, setHistoryLoaded] = useState(false)
  const loadingRef = useRef(false)

  // New streaming state
  const [streamingState, setStreamingState] = useState<StreamingState>({
    isStreaming: false,
    connectionState: 'disconnected',
    activeTask: null,
    reconnectAttempts: 0,
    lastError: undefined
  })

  // Streaming control refs
  const streamingControlRef = useRef<{
    cancel: () => void
  } | null>(null)

  // Check if streaming is enabled and supported
  const isStreamingEnabled = options.enableStreaming !== false && isStreamingSupported()

  // Message threading state for streaming
  const messageThreadRef = useRef<Map<string, Message[]>>(new Map())
  const messageSequenceRef = useRef<number>(0)

  // Task state management
  const [taskHistory, setTaskHistory] = useState<Map<string, TaskInfo>>(new Map())
  const taskTimeoutRef = useRef<Map<string, NodeJS.Timeout>>(new Map())

  /**
   * Process streaming message with proper ordering and threading
   */
  const processStreamingMessage = useCallback((message: Message, command: string) => {
    const taskId = message.metadata?.task_id as string
    const messageType = message.metadata?.type as string

    // Ensure message ordering by adding sequence number
    const sequencedMessage = {
      ...message,
      metadata: {
        ...message.metadata,
        sequence: messageSequenceRef.current++,
        threadId: taskId
      }
    }

    // Handle different message types
    switch (messageType) {
      case 'task_start': {
        // Initialize message thread for this task
        messageThreadRef.current.set(taskId, [sequencedMessage])

        // Update task state
        const taskInfo: TaskInfo = {
          id: taskId,
          sandbox_id: sandboxId,
          command,
          status: 'running',
          created_at: message.metadata?.timestamp || new Date().toISOString(),
          started_at: message.metadata?.timestamp
        }

        setStreamingState(prev => ({
          ...prev,
          connectionState: 'connected',
          activeTask: taskInfo
        }))

        // Add start message to UI
        setMessages(prev => [...prev, sequencedMessage])
        options.onTaskStart?.(taskInfo)
        break
      }

      case 'task_log': {
        // Add log message to thread
        const existingThread = messageThreadRef.current.get(taskId) || []
        messageThreadRef.current.set(taskId, [...existingThread, sequencedMessage])

        // Add log message to UI (maintain ordering)
        setMessages(prev => {
          // Find insertion point to maintain order
          const newMessages = [...prev, sequencedMessage]
          return newMessages.sort((a, b) => {
            const seqA = a.metadata?.sequence as number || 0
            const seqB = b.metadata?.sequence as number || 0
            return seqA - seqB
          })
        })
        break
      }

      case 'task_complete': {
        // Add completion message to thread
        const completeThread = messageThreadRef.current.get(taskId) || []
        messageThreadRef.current.set(taskId, [...completeThread, sequencedMessage])

        // Update task state
        const completedTaskInfo: TaskInfo = {
          id: taskId,
          sandbox_id: sandboxId,
          command,
          status: 'completed',
          created_at: streamingState.activeTask?.created_at || new Date().toISOString(),
          started_at: streamingState.activeTask?.started_at,
          completed_at: message.metadata?.timestamp,
          exit_code: message.metadata?.exit_code as number,
          execution_time: message.metadata?.execution_time as number
        }

        setStreamingState(prev => ({
          ...prev,
          activeTask: completedTaskInfo
        }))

        // Add completion message to UI
        setMessages(prev => [...prev, sequencedMessage])
        options.onTaskComplete?.(completedTaskInfo)
        break
      }

      case 'task_error': {
        // Add error message to thread
        const errorThread = messageThreadRef.current.get(taskId) || []
        messageThreadRef.current.set(taskId, [...errorThread, sequencedMessage])

        // Create task error
        const taskError: TaskError = {
          code: 'TASK_EXECUTION_ERROR',
          message: message.content,
          task_id: taskId,
          recoverable: false,
          timestamp: message.metadata?.timestamp
        }

        setStreamingState(prev => ({
          ...prev,
          connectionState: 'error',
          lastError: taskError
        }))

        // Add error message to UI
        setMessages(prev => [...prev, sequencedMessage])
        options.onTaskError?.(taskError)
        break
      }

      default: {
        // Handle unknown message types
        setMessages(prev => [...prev, sequencedMessage])
        break
      }
    }
  }, [sandboxId, streamingState.activeTask, setMessages, options])

  // Use AI SDK's useChat hook with custom configuration
  const {
    messages,
    input,
    setInput,
    isLoading,
    setMessages
  } = useChat({
    initialMessages: options.initialMessages || [],
    onError: (error) => {
      setError(error.message)
      options.onError?.(error)
    },
    // We'll handle API calls manually through FastAPI adapter
    api: '/api/chat/dummy', // Dummy endpoint, won't be used
    onFinish: () => {
      // This won't be called since we handle everything manually
    }
  })

  /**
   * Load initial chat history
   */
  const loadHistory = useCallback(async () => {
    if (loadingRef.current || historyLoaded) return
    
    loadingRef.current = true
    setIsLoadingHistory(true)
    setError(null)

    try {
      const response = await adapter.loadHistory(sandboxId, 1)
      
      setMessages(response.messages)
      setPagination({
        page: response.pagination.page,
        total: response.pagination.total,
        hasNextPage: response.pagination.hasNextPage
      })
      
      setHistoryLoaded(true)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load history'
      setError(errorMessage)
      console.error('Failed to load chat history:', err)
    } finally {
      setIsLoadingHistory(false)
      loadingRef.current = false
    }
  }, [sandboxId, adapter, setMessages, historyLoaded])

  /**
   * Load more history for pagination
   */
  const loadMoreHistory = useCallback(async () => {
    if (!pagination.hasNextPage || isLoadingHistory) return

    setIsLoadingHistory(true)
    try {
      const response = await adapter.loadHistory(sandboxId, pagination.page + 1)
      
      // Prepend older messages
      setMessages(prevMessages => [...response.messages, ...prevMessages])
      setPagination({
        page: response.pagination.page,
        total: response.pagination.total,
        hasNextPage: response.pagination.hasNextPage
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load more history'
      setError(errorMessage)
    } finally {
      setIsLoadingHistory(false)
    }
  }, [sandboxId, adapter, pagination, isLoadingHistory, setMessages])

  /**
   * Execute command with streaming support
   */
  const executeCommandWithStreaming = useCallback(async (command: string) => {
    if (!isStreamingEnabled) {
      // Fall back to regular execution
      return executeCommandRegular(command)
    }

    setIsExecutingCommand(true)
    setError(null)

    // Update streaming state
    setStreamingState(prev => ({
      ...prev,
      isStreaming: true,
      connectionState: 'connecting',
      activeTask: {
        id: `temp-${Date.now()}`,
        sandbox_id: sandboxId,
        command,
        status: 'pending',
        created_at: new Date().toISOString()
      }
    }))

    try {
      const streamingControl = await adapter.startStreamingTask(sandboxId, command)
      streamingControlRef.current = streamingControl

      // Handle streaming messages with proper ordering and threading
      streamingControl.onMessage((message) => {
        // Process message based on type for proper state management
        processStreamingMessage(message, command)
      })

      // Handle streaming errors
      streamingControl.onError((taskError) => {
        setError(taskError.message)
        setStreamingState(prev => ({
          ...prev,
          connectionState: 'error',
          lastError: taskError
        }))

        options.onTaskError?.(taskError)
      })

      // Handle completion
      streamingControl.onComplete(() => {
        setStreamingState(prev => ({
          ...prev,
          isStreaming: false,
          connectionState: 'disconnected',
          activeTask: null
        }))
        setIsExecutingCommand(false)
        streamingControlRef.current = null
      })

      options.onCommandExecute?.(command, sandboxId)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Streaming command execution failed'
      setError(errorMessage)

      setStreamingState(prev => ({
        ...prev,
        isStreaming: false,
        connectionState: 'error',
        activeTask: null,
        lastError: {
          code: 'STREAMING_ERROR',
          message: errorMessage,
          recoverable: true
        }
      }))

      setIsExecutingCommand(false)
      streamingControlRef.current = null

      // Fall back to regular execution
      await executeCommandRegular(command)
    }
  }, [sandboxId, adapter, isStreamingEnabled, options, streamingState.activeTask, executeCommandRegular, setMessages, processStreamingMessage])

  /**
   * Execute command in sandbox (regular non-streaming)
   */
  const executeCommandRegular = useCallback(async (command: string) => {
    setIsExecutingCommand(true)
    setError(null)

    try {
      const response = await adapter.executeCommand(sandboxId, command)
      const { content, isError } = adapter.formatCommandOutput(response)
      
      
      // Send the command result to backend for persistence
      const resultContent = content || '(No output)'
      const messageType = isError ? 'error' : 'system'
      
      try {
        // Persist the command result to backend with metadata
        const commandMetadata = {
          exitCode: response.exit_code,
          executionTime: response.execution_time,
          originalCommand: command
        }
        
        await adapter.sendMessageWithMetadata(
          sandboxId,
          resultContent,
          messageType as 'user' | 'system' | 'error' | 'status',
          commandMetadata
        )
        
        // Refresh history to get the persisted message with correct backend ID and metadata
        // This ensures persistence and prevents messages from disappearing on reload
        const historyResponse = await adapter.loadHistory(sandboxId, 1)
        setMessages(historyResponse.messages)
        setPagination({
          page: historyResponse.pagination.page,
          total: historyResponse.pagination.total,
          hasNextPage: historyResponse.pagination.hasNextPage
        })
      } catch (persistError) {
        console.error('Failed to persist command result:', persistError)
        // Still show the result locally even if persistence fails
        const resultMessage = isError 
          ? adapter.createErrorMessage(sandboxId, resultContent, {
              exitCode: response.exit_code,
              executionTime: response.execution_time,
              originalCommand: command
            })
          : adapter.createSystemMessage(sandboxId, resultContent, {
              exitCode: response.exit_code,
              executionTime: response.execution_time,
              originalCommand: command
            })

        setMessages(prev => [...prev, resultMessage])
      }
      
      options.onCommandExecute?.(command, sandboxId)
    } catch (err) {
      
      const errorMessage = err instanceof Error ? err.message : 'Command execution failed'
      
      try {
        // Persist error to backend with metadata
        const errorMetadata = { originalCommand: command }
        
        await adapter.sendMessageWithMetadata(sandboxId, errorMessage, 'error', errorMetadata)
        
        // Refresh history to get the persisted error message
        const historyResponse = await adapter.loadHistory(sandboxId, 1)
        setMessages(historyResponse.messages)
        setPagination({
          page: historyResponse.pagination.page,
          total: historyResponse.pagination.total,
          hasNextPage: historyResponse.pagination.hasNextPage
        })
      } catch (persistError) {
        console.error('Failed to persist error message:', persistError)
        
        // Fallback: show local error message
        const errorMsg = adapter.createErrorMessage(sandboxId, errorMessage, {
          originalCommand: command
        })
        
        setMessages(prev => [...prev, errorMsg])
      }
      setError(errorMessage)
    } finally {
      setIsExecutingCommand(false)
    }
  }, [sandboxId, adapter, setMessages, options])

  /**
   * Get messages for a specific task thread
   */
  const getTaskMessages = useCallback((taskId: string): Message[] => {
    return messageThreadRef.current.get(taskId) || []
  }, [])

  /**
   * Get all active task threads
   */
  const getActiveThreads = useCallback((): Map<string, Message[]> => {
    return new Map(messageThreadRef.current)
  }, [])

  /**
   * Cancel active streaming task
   */
  const cancelTask = useCallback(() => {
    if (streamingControlRef.current) {
      streamingControlRef.current.cancel()
      streamingControlRef.current = null
    }

    // Clean up current task thread
    if (streamingState.activeTask?.id) {
      messageThreadRef.current.delete(streamingState.activeTask.id)
    }

    setStreamingState(prev => ({
      ...prev,
      isStreaming: false,
      connectionState: 'disconnected',
      activeTask: null
    }))

    setIsExecutingCommand(false)
  }, [streamingState.activeTask?.id])

  /**
   * Execute command (with streaming support)
   */
  const executeCommand = useCallback(async (command: string) => {
    return executeCommandWithStreaming(command)
  }, [executeCommandWithStreaming])

  /**
   * Custom submit handler that uses FastAPI backend with streaming support
   */
  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    e?.preventDefault()

    if (!input.trim() || isLoading || isExecutingCommand) return

    const message = input.trim()
    setInput('') // Clear input immediately for better UX
    setError(null)

    // Add optimistic user message
    const userMessage = adapter.createOptimisticMessage(sandboxId, message)
    setMessages(prev => [...prev, userMessage])

    try {
      // Send message to FastAPI backend for persistence
      await adapter.sendMessage(sandboxId, message, 'user')
      
      // Check if this is a command that should be executed
      if (adapter.isExecutableCommand(message)) {
        await executeCommand(message)
      } else {
        // For non-commands, refresh history to get the persisted message with correct backend ID
        try {
          const historyResponse = await adapter.loadHistory(sandboxId, 1)
          setMessages(historyResponse.messages)
          setPagination({
            page: historyResponse.pagination.page,
            total: historyResponse.pagination.total,
            hasNextPage: historyResponse.pagination.hasNextPage
          })
        } catch (historyError) {
          console.error('Failed to refresh history after sending message:', historyError)
          // Keep the optimistic message if history refresh fails
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message'
      setError(errorMessage)
      
      // Remove the optimistic message since sending failed
      setMessages(prev => prev.filter(m => m.id !== userMessage.id))
      
      // Add error message to chat and persist it
      try {
        await adapter.sendMessage(sandboxId, errorMessage, 'error')
      } catch (persistError) {
        console.error('Failed to persist error message:', persistError)
      }
      
      const errorMsg = adapter.createErrorMessage(sandboxId, errorMessage)
      setMessages(prev => [...prev, errorMsg])
    }
  }, [input, isLoading, isExecutingCommand, sandboxId, adapter, setInput, setMessages, executeCommand])

  /**
   * Clear chat history
   */
  const clearHistory = useCallback(async () => {
    try {
      await adapter.clearHistory(sandboxId)
      setMessages([])
      setPagination({
        page: 1,
        total: 0,
        hasNextPage: false
      })
      setHistoryLoaded(false)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear history'
      setError(errorMessage)
    }
  }, [sandboxId, adapter, setMessages])

  /**
   * Reload current conversation
   */
  const reloadConversation = useCallback(() => {
    setHistoryLoaded(false)
    loadHistory()
  }, [loadHistory])

  // Load history on mount or sandboxId change
  useEffect(() => {
    if (sandboxId) {
      setHistoryLoaded(false)
      loadHistory()
    }
  }, [sandboxId, loadHistory]) // Only depend on sandboxId to avoid infinite loops

  // Cleanup streaming connections and message threads on unmount
  useEffect(() => {
    return () => {
      if (streamingControlRef.current) {
        streamingControlRef.current.cancel()
        streamingControlRef.current = null
      }
      // Clear message threads
      messageThreadRef.current.clear()
      messageSequenceRef.current = 0
    }
  }, [])

  // Cleanup completed task threads periodically
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      const now = Date.now()
      const maxAge = 5 * 60 * 1000 // 5 minutes
      const messageThreads = messageThreadRef.current

      messageThreads.forEach((thread, taskId) => {
        const lastMessage = thread[thread.length - 1]
        const messageTime = lastMessage?.createdAt?.getTime() || 0

        if (now - messageTime > maxAge) {
          messageThreads.delete(taskId)
        }
      })
    }, 60000) // Run every minute

    return () => clearInterval(cleanupInterval)
  }, [])

  return {
    messages,
    input,
    setInput,
    handleSubmit,
    isLoading: isLoading || false, // AI SDK isLoading
    isLoadingHistory,
    isSendingMessage: isLoading || isExecutingCommand, // Map AI SDK loading and command execution to our sending state
    error,
    reload: reloadConversation,
    clearHistory,
    executeCommand,
    pagination,
    loadMoreHistory,
    // New streaming properties
    activeTask: streamingState.activeTask,
    cancelTask,
    isStreaming: streamingState.isStreaming,
    streamingState,
    // Message threading utilities
    getTaskMessages,
    getActiveThreads
  }
}
