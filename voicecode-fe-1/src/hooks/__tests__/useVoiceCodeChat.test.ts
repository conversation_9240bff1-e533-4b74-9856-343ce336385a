/**
 * Tests for useVoiceCodeChat hook with streaming support
 */

import { renderHook, act } from '@testing-library/react'
import { useVoiceCodeChat } from '../useVoiceCodeChat'
import { FastAPIAdapter } from '@/lib/fastapi-adapter'
import type { Message } from 'ai'

// Mock the FastAPI adapter
jest.mock('@/lib/fastapi-adapter')
jest.mock('@/utils/streaming', () => ({
  isStreamingSupported: jest.fn(() => true),
  parseSSEMessage: jest.fn(),
  buildStreamingUrl: jest.fn(() => 'http://localhost:8000/api/sandbox/test/tasks/stream'),
  createTaskError: jest.fn(),
}))

const MockedFastAPIAdapter = FastAPIAdapter as jest.MockedClass<typeof FastAPIAdapter>

describe('useVoiceCodeChat', () => {
  const mockSandboxId = 'test-sandbox-123'
  let mockAdapter: jest.Mocked<FastAPIAdapter>

  beforeEach(() => {
    jest.clearAllMocks()
    
    mockAdapter = {
      loadHistory: jest.fn(),
      sendMessage: jest.fn(),
      executeCommand: jest.fn(),
      clearHistory: jest.fn(),
      isExecutableCommand: jest.fn(),
      formatCommandOutput: jest.fn(),
      sendMessageWithMetadata: jest.fn(),
      createOptimisticMessage: jest.fn(),
      createSystemMessage: jest.fn(),
      createErrorMessage: jest.fn(),
      startStreamingTask: jest.fn(),
    } as unknown as jest.Mocked<FastAPIAdapter>

    MockedFastAPIAdapter.mockImplementation(() => mockAdapter)
  })

  describe('Basic functionality', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useVoiceCodeChat(mockSandboxId))

      expect(result.current.messages).toEqual([])
      expect(result.current.input).toBe('')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.isLoadingHistory).toBe(false)
      expect(result.current.error).toBe(null)
      expect(result.current.isStreaming).toBe(false)
      expect(result.current.activeTask).toBe(null)
    })

    it('should load history on mount', async () => {
      const mockMessages: Message[] = [
        {
          id: '1',
          role: 'user',
          content: 'Hello',
          createdAt: new Date(),
        }
      ]

      mockAdapter.loadHistory.mockResolvedValue({
        messages: mockMessages,
        pagination: {
          page: 1,
          total: 1,
          pageSize: 50,
          hasNextPage: false
        }
      })

      const { result } = renderHook(() => useVoiceCodeChat(mockSandboxId))

      // Wait for the effect to run
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0))
      })

      expect(mockAdapter.loadHistory).toHaveBeenCalledWith(mockSandboxId, 1)
    })
  })

  describe('Streaming functionality', () => {
    it('should initialize streaming state correctly', () => {
      const { result } = renderHook(() => 
        useVoiceCodeChat(mockSandboxId, { enableStreaming: true })
      )

      expect(result.current.streamingState).toEqual({
        isStreaming: false,
        connectionState: 'disconnected',
        activeTask: null,
        reconnectAttempts: 0,
        lastError: undefined
      })
    })

    it('should provide cancelTask function', () => {
      const { result } = renderHook(() => useVoiceCodeChat(mockSandboxId))

      expect(typeof result.current.cancelTask).toBe('function')
    })

    it('should handle streaming disabled gracefully', () => {
      const { result } = renderHook(() => 
        useVoiceCodeChat(mockSandboxId, { enableStreaming: false })
      )

      expect(result.current.isStreaming).toBe(false)
      expect(result.current.activeTask).toBe(null)
    })
  })

  describe('Message handling', () => {
    it('should handle input changes', () => {
      const { result } = renderHook(() => useVoiceCodeChat(mockSandboxId))

      act(() => {
        result.current.setInput('test message')
      })

      expect(result.current.input).toBe('test message')
    })

    it('should clear input on submit', async () => {
      mockAdapter.sendMessage.mockResolvedValue({
        messageId: 'msg-1',
        status: 'sent'
      })
      
      mockAdapter.isExecutableCommand.mockReturnValue(false)
      mockAdapter.createOptimisticMessage.mockReturnValue({
        id: 'temp-1',
        role: 'user',
        content: 'test message',
        createdAt: new Date()
      })

      const { result } = renderHook(() => useVoiceCodeChat(mockSandboxId))

      act(() => {
        result.current.setInput('test message')
      })

      await act(async () => {
        await result.current.handleSubmit()
      })

      expect(result.current.input).toBe('')
    })
  })

  describe('Error handling', () => {
    it('should handle streaming errors gracefully', async () => {
      const mockError = new Error('Streaming failed')
      mockAdapter.startStreamingTask.mockRejectedValue(mockError)
      mockAdapter.executeCommand.mockResolvedValue({
        result: 'fallback result',
        exit_code: 0,
        execution_time: 1.5
      })
      mockAdapter.formatCommandOutput.mockReturnValue({
        content: 'fallback result',
        isError: false
      })

      const { result } = renderHook(() => 
        useVoiceCodeChat(mockSandboxId, { enableStreaming: true })
      )

      await act(async () => {
        await result.current.executeCommand('echo test')
      })

      // Should fall back to regular execution
      expect(mockAdapter.executeCommand).toHaveBeenCalled()
    })
  })
})
