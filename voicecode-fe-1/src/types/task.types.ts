/**
 * Task-related TypeScript types for streaming integration
 */

export interface TaskInfo {
  id: string
  sandbox_id: string
  command: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  created_at: string
  started_at?: string
  completed_at?: string
  exit_code?: number
  execution_time?: number
  working_directory?: string
}

export interface TaskError {
  code: string
  message: string
  task_id?: string
  recoverable: boolean
  timestamp?: string
}

export interface TaskExecutionRequest {
  command: string
  working_directory?: string
}

export interface TaskStreamResponse {
  task_id: string
  status: string
  stream_url?: string
  message: string
}

export interface SSEMessage {
  id: string
  role: string
  content: string
  metadata?: {
    type: 'task_start' | 'task_log' | 'task_complete' | 'task_error'
    task_id: string
    timestamp?: string
    exit_code?: number
    execution_time?: number
    log_type?: string
    original_timestamp?: string
  }
}

export interface TaskMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  createdAt?: Date
  metadata?: {
    type: 'task_start' | 'task_log' | 'task_complete' | 'task_error'
    task_id: string
    timestamp?: string
    exit_code?: number
    execution_time?: number
    originalCommand?: string
  }
}

// Connection states for streaming
export type StreamingConnectionState = 
  | 'disconnected' 
  | 'connecting' 
  | 'connected' 
  | 'error' 
  | 'reconnecting'

export interface StreamingState {
  isStreaming: boolean
  connectionState: StreamingConnectionState
  activeTask: TaskInfo | null
  reconnectAttempts: number
  lastError?: TaskError
}

// Type guards
export const isTaskMessage = (message: unknown): message is TaskMessage => {
  if (!message || typeof message !== 'object') return false

  const msg = message as Record<string, unknown>
  const metadata = msg.metadata as Record<string, unknown> | undefined

  return (
    typeof msg.id === 'string' &&
    typeof msg.role === 'string' &&
    typeof msg.content === 'string' &&
    metadata !== undefined &&
    typeof metadata === 'object' &&
    typeof metadata.type === 'string' &&
    ['task_start', 'task_log', 'task_complete', 'task_error'].includes(metadata.type as string)
  )
}

export const isSSEMessage = (data: unknown): data is SSEMessage => {
  if (!data || typeof data !== 'object') return false

  const msg = data as Record<string, unknown>
  const metadata = msg.metadata as Record<string, unknown> | undefined

  return (
    typeof msg.id === 'string' &&
    typeof msg.role === 'string' &&
    typeof msg.content === 'string' &&
    metadata !== undefined &&
    typeof metadata === 'object' &&
    typeof metadata.type === 'string' &&
    ['task_start', 'task_log', 'task_complete', 'task_error'].includes(metadata.type as string)
  )
}
