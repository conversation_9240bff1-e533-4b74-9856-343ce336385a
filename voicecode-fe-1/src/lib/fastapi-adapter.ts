import type { Message } from 'ai'
import { ChatService } from '@/services/chat.service'
import type { ChatMessage, ChatMessageType } from '@/types/chat.types'
import type { CommandExecuteResponse } from '@/types/command.types'
import type { SSEMessage, TaskExecutionRequest, TaskError } from '@/types/task.types'
import { parseSSEMessage, buildStreamingUrl, createTaskError } from '@/utils/streaming'

/**
 * FastAPI adapter for Vercel AI SDK
 * Bridges existing FastAPI backend with AI SDK patterns
 */
export class FastAPIAdapter {
  constructor() {}

  /**
   * Convert FastAPI ChatMessage to AI SDK Message format
   */
  static convertToAIMessage(fastApiMessage: ChatMessage): Message & { metadata?: Record<string, unknown> } {
    // Map FastAPI message types to AI SDK roles
    const role = FastAPIAdapter.mapMessageTypeToRole(fastApiMessage.messageType)
    
    return {
      id: fastApiMessage.id,
      role,
      content: fastApiMessage.content,
      createdAt: fastApiMessage.createdAt,
      metadata: {
        // Preserve all original metadata from backend
        ...fastApiMessage.metadata,
        // Add our standard fields
        sandboxId: fastApiMessage.sandboxId,
        userId: fastApiMessage.userId,
        messageType: fastApiMessage.messageType,
        commandId: fastApiMessage.commandId
      }
    } as Message & { metadata?: Record<string, unknown> }
  }

  /**
   * Convert AI SDK Message to FastAPI ChatMessage format
   */
  static convertFromAIMessage(aiMessage: Message & { metadata?: Record<string, unknown> }, sandboxId: string): ChatMessage {
    const messageType = FastAPIAdapter.mapRoleToMessageType(aiMessage.role)
    
    return {
      id: aiMessage.id,
      sandboxId,
      userId: (aiMessage.metadata?.userId as string) || 'current-user',
      messageType,
      content: aiMessage.content,
      commandId: aiMessage.metadata?.commandId as string | undefined,
      createdAt: aiMessage.createdAt || new Date(),
      metadata: aiMessage.metadata
    }
  }

  /**
   * Map FastAPI message types to AI SDK roles
   */
  private static mapMessageTypeToRole(messageType: ChatMessageType): Message['role'] {
    switch (messageType) {
      case 'user':
        return 'user'
      case 'system':
      case 'error':
      case 'status':
      default:
        return 'assistant'
    }
  }

  /**
   * Map AI SDK roles to FastAPI message types
   */
  private static mapRoleToMessageType(role: Message['role']): ChatMessageType {
    switch (role) {
      case 'user':
        return 'user'
      case 'assistant':
      case 'system':
      default:
        return 'system'
    }
  }

  /**
   * Load chat history and convert to AI SDK format
   */
  async loadHistory(sandboxId: string, page = 1): Promise<{
    messages: Message[]
    pagination: {
      page: number
      total: number
      pageSize: number
      hasNextPage: boolean
    }
  }> {
    try {
      const response = await ChatService.loadHistory(sandboxId, { page })
      
      const messages = response.messages.map(msg => 
        FastAPIAdapter.convertToAIMessage(msg)
      )

      return {
        messages,
        pagination: {
          page: response.page,
          total: response.total,
          pageSize: response.pageSize,
          hasNextPage: response.hasNextPage
        }
      }
    } catch (error) {
      console.error('Failed to load history:', error)
      throw new Error('Failed to load chat history')
    }
  }

  /**
   * Send message using FastAPI backend
   */
  async sendMessage(
    sandboxId: string, 
    content: string, 
    messageType: ChatMessageType = 'user'
  ): Promise<{
    messageId: string
    status: string
    commandId?: string
  }> {
    try {
      const response = await ChatService.sendMessage(sandboxId, content, messageType)
      return response
    } catch (error) {
      console.error('Failed to send message:', error)
      throw new Error('Failed to send message')
    }
  }

  /**
   * Send message with metadata (for command results)
   */
  async sendMessageWithMetadata(
    sandboxId: string,
    content: string,
    messageType: ChatMessageType,
    metadata: Record<string, unknown> = {}
  ): Promise<{
    messageId: string
    status: string
    commandId?: string
  }> {
    try {

      
      const response = await ChatService.sendMessage(sandboxId, content, messageType, metadata)
      return response
    } catch (error) {
      console.error('Failed to send message with metadata:', error)
      throw new Error('Failed to send message with metadata')
    }
  }

  /**
   * Execute command in sandbox
   */
  async executeCommand(sandboxId: string, command: string): Promise<CommandExecuteResponse> {
    try {
      const response = await ChatService.executeCommand(sandboxId, command)
      return response
    } catch (error) {
      console.error('Failed to execute command:', error)
      throw new Error('Failed to execute command')
    }
  }

  /**
   * Clear chat history
   */
  async clearHistory(sandboxId: string): Promise<{ success: boolean; deletedCount: number }> {
    try {
      const response = await ChatService.clearHistory(sandboxId)
      return response
    } catch (error) {
      console.error('Failed to clear history:', error)
      throw new Error('Failed to clear chat history')
    }
  }

  /**
   * Check if message is an executable command
   */
  isExecutableCommand(content: string): boolean {
    return ChatService.isExecutableCommand(content)
  }

  /**
   * Format command output for display
   */
  formatCommandOutput(response: CommandExecuteResponse): {
    content: string
    isError: boolean
  } {
    return ChatService.formatCommandOutput(response)
  }

  /**
   * Create an optimistic message for immediate UI feedback
   */
  createOptimisticMessage(sandboxId: string, content: string): Message {
    const tempMessage: ChatMessage = {
      id: `temp-${Date.now()}`,
      sandboxId,
      userId: 'current-user',
      messageType: 'user',
      content: content.trim(),
      createdAt: new Date(),
      metadata: {}
    }

    return FastAPIAdapter.convertToAIMessage(tempMessage)
  }

  /**
   * Create system message for command execution feedback
   */
  createSystemMessage(
    sandboxId: string, 
    content: string, 
    metadata?: Record<string, unknown>
  ): Message {
    const systemMessage: ChatMessage = {
      id: `system-${Date.now()}`,
      sandboxId,
      userId: 'system',
      messageType: 'system',
      content,
      createdAt: new Date(),
      metadata: metadata || {}
    }

    return FastAPIAdapter.convertToAIMessage(systemMessage)
  }

  /**
   * Create error message
   */
  createErrorMessage(
    sandboxId: string,
    content: string,
    metadata?: Record<string, unknown>
  ): Message {
    const errorMessage: ChatMessage = {
      id: `error-${Date.now()}`,
      sandboxId,
      userId: 'system',
      messageType: 'error',
      content,
      createdAt: new Date(),
      metadata: metadata || {}
    }

    return FastAPIAdapter.convertToAIMessage(errorMessage)
  }

  /**
   * Convert SSE message to AI SDK Message format
   */
  static convertSSEToAIMessage(sseMessage: SSEMessage, sandboxId: string): Message & { metadata?: Record<string, unknown> } {
    return {
      id: sseMessage.id,
      role: sseMessage.role as Message['role'],
      content: sseMessage.content,
      createdAt: sseMessage.metadata?.timestamp ? new Date(sseMessage.metadata.timestamp) : new Date(),
      metadata: {
        ...sseMessage.metadata,
        sandboxId,
        messageType: 'system' // SSE messages are system messages
      }
    }
  }

  /**
   * Start streaming task execution
   */
  async startStreamingTask(
    sandboxId: string,
    command: string,
    workingDirectory?: string
  ): Promise<{
    onMessage: (callback: (message: Message) => void) => void
    onError: (callback: (error: TaskError) => void) => void
    onComplete: (callback: () => void) => void
    cancel: () => void
  }> {
    const url = buildStreamingUrl(sandboxId)
    const abortController = new AbortController()

    let messageCallback: ((message: Message) => void) | null = null
    let errorCallback: ((error: TaskError) => void) | null = null
    let completeCallback: (() => void) | null = null

    try {
      // Start the streaming task
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream'
        },
        body: JSON.stringify({
          command: command.trim(),
          working_directory: workingDirectory || '/home/<USER>/workspace/repository'
        } as TaskExecutionRequest),
        signal: abortController.signal
      })

      if (!response.ok) {
        throw new Error(`Streaming request failed: ${response.status} ${response.statusText}`)
      }

      // Create EventSource-like behavior with fetch stream
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body available for streaming')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      const processStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read()

            if (done) {
              completeCallback?.()
              break
            }

            buffer += decoder.decode(value, { stream: true })
            const lines = buffer.split('\n')
            buffer = lines.pop() || '' // Keep incomplete line in buffer

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6) // Remove 'data: ' prefix

                if (data === '[DONE]') {
                  completeCallback?.()
                  return
                }

                const sseMessage = parseSSEMessage(data)
                if (sseMessage && messageCallback) {
                  const aiMessage = FastAPIAdapter.convertSSEToAIMessage(sseMessage, sandboxId)
                  messageCallback(aiMessage)
                }
              }
            }
          }
        } catch (error) {
          if (!abortController.signal.aborted) {
            const taskError = createTaskError(error)
            errorCallback?.(taskError)
          }
        }
      }

      // Start processing stream
      processStream()

    } catch (error) {
      const taskError = createTaskError(error)
      setTimeout(() => errorCallback?.(taskError), 0)
    }

    return {
      onMessage: (callback) => { messageCallback = callback },
      onError: (callback) => { errorCallback = callback },
      onComplete: (callback) => { completeCallback = callback },
      cancel: () => {
        abortController.abort()
        completeCallback?.()
      }
    }
  }
}

/**
 * Create adapter instance with authentication
 */
export function createFastAPIAdapter(): FastAPIAdapter {
  return new FastAPIAdapter()
}
