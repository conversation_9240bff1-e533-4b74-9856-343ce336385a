/**
 * Streaming utilities for SSE connection management and message processing
 */

import type { SSEMessage, TaskError, StreamingConnectionState } from '@/types/task.types'

export interface StreamingOptions {
  enableStreaming?: boolean
  maxReconnectAttempts?: number
  reconnectDelay?: number
  connectionTimeout?: number
}

export const DEFAULT_STREAMING_OPTIONS: Required<StreamingOptions> = {
  enableStreaming: true,
  maxReconnectAttempts: 3,
  reconnectDelay: 1000,
  connectionTimeout: 30000
}

/**
 * Parse SSE message from event data
 */
export function parseSSEMessage(data: string): SSEMessage | null {
  try {
    if (data === '[DONE]') {
      return null // Stream termination signal
    }
    
    const parsed = JSON.parse(data)
    
    // Validate required fields
    if (!parsed.id || !parsed.role || !parsed.content) {
      console.warn('Invalid SSE message format:', parsed)
      return null
    }
    
    return parsed as SSEMessage
  } catch (error) {
    console.error('Failed to parse SSE message:', error, 'Data:', data)
    return null
  }
}

/**
 * Create EventSource with proper error handling and reconnection
 */
export function createEventSource(
  url: string,
  options: {
    onMessage: (message: SSEMessage) => void
    onError: (error: TaskError) => void
    onConnectionChange: (state: StreamingConnectionState) => void
    signal?: AbortSignal
  }
): EventSource {
  const eventSource = new EventSource(url)
  
  let connectionTimeout: NodeJS.Timeout | null = null
  
  // Set connection timeout
  connectionTimeout = setTimeout(() => {
    options.onError({
      code: 'CONNECTION_TIMEOUT',
      message: 'Connection timeout',
      recoverable: true
    })
    eventSource.close()
  }, DEFAULT_STREAMING_OPTIONS.connectionTimeout)
  
  eventSource.onopen = () => {
    if (connectionTimeout) {
      clearTimeout(connectionTimeout)
      connectionTimeout = null
    }
    options.onConnectionChange('connected')
  }
  
  eventSource.onmessage = (event) => {
    const message = parseSSEMessage(event.data)
    if (message) {
      options.onMessage(message)
    } else if (event.data === '[DONE]') {
      // Stream completed normally
      eventSource.close()
      options.onConnectionChange('disconnected')
    }
  }
  
  eventSource.onerror = () => {
    if (connectionTimeout) {
      clearTimeout(connectionTimeout)
      connectionTimeout = null
    }
    
    const error: TaskError = {
      code: 'CONNECTION_ERROR',
      message: 'Streaming connection error',
      recoverable: eventSource.readyState !== EventSource.CLOSED
    }
    
    options.onError(error)
    
    if (eventSource.readyState === EventSource.CLOSED) {
      options.onConnectionChange('disconnected')
    } else {
      options.onConnectionChange('error')
    }
  }
  
  // Handle abort signal
  if (options.signal) {
    options.signal.addEventListener('abort', () => {
      if (connectionTimeout) {
        clearTimeout(connectionTimeout)
      }
      eventSource.close()
      options.onConnectionChange('disconnected')
    })
  }
  
  return eventSource
}

/**
 * Build streaming URL with authentication
 */
export function buildStreamingUrl(sandboxId: string, baseUrl?: string): string {
  const base = baseUrl || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
  return `${base}/api/sandbox/${sandboxId}/tasks/stream`
}

/**
 * Debounce function for rapid message updates
 */
export function debounce<T extends (...args: unknown[]) => void>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

/**
 * Create task error from various error sources
 */
export function createTaskError(
  error: unknown,
  taskId?: string,
  recoverable = true
): TaskError {
  if (error instanceof Error) {
    return {
      code: 'TASK_ERROR',
      message: error.message,
      task_id: taskId,
      recoverable,
      timestamp: new Date().toISOString()
    }
  }
  
  if (typeof error === 'string') {
    return {
      code: 'TASK_ERROR',
      message: error,
      task_id: taskId,
      recoverable,
      timestamp: new Date().toISOString()
    }
  }
  
  return {
    code: 'UNKNOWN_ERROR',
    message: 'An unknown error occurred',
    task_id: taskId,
    recoverable,
    timestamp: new Date().toISOString()
  }
}

/**
 * Validate streaming support
 */
export function isStreamingSupported(): boolean {
  return typeof EventSource !== 'undefined'
}

/**
 * Get connection state display text
 */
export function getConnectionStateText(state: StreamingConnectionState): string {
  switch (state) {
    case 'connecting':
      return 'Connecting...'
    case 'connected':
      return 'Connected'
    case 'disconnected':
      return 'Disconnected'
    case 'error':
      return 'Connection Error'
    case 'reconnecting':
      return 'Reconnecting...'
    default:
      return 'Unknown'
  }
}
